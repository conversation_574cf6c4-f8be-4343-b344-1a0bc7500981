package com.shoalter.mms.store.api.adapter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shoalter.mms.store.api.adapter.controller.dto.ResponseDto;
import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.mms.product.dto.PageResult;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkCreateRequest;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkPageResponse;
import com.shoalter.mms.store.api.adapter.mms.referral_link.usecase.ReferralLinkUseCase;
import com.shoalter.mms.store.api.enums.ResponseStatusCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class ReferralLinkControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ReferralLinkUseCase referralLinkUseCase;

    private ReferralLinkCreateRequest createRequest;
    private ReferralLinkPageResponse pageResponse;

    @BeforeEach
    void setUp() {
        createRequest = new ReferralLinkCreateRequest();
        createRequest.setStorefrontStoreCode("H000001");
        createRequest.setPageUrl("https://www.hktvmall.com/s/H000001");

        pageResponse = ReferralLinkPageResponse.builder()
            .link("https://short.link/abc123")
            .target("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&openinapp=true&autoTriggerApp=true&fastrender=true&backstack=true")
            .pageUrl("https://www.hktvmall.com/s/H000001")
            .storeCode("H000001")
            .createdTime(System.currentTimeMillis())
            .build();
    }

    @Test
    void createReferralLink_ShouldReturnBothLinkAndTarget() throws Exception {
        // Given
        ResponseDto<ReferralLinkPageResponse> responseDto = ResponseDto.success(pageResponse);
        when(referralLinkUseCase.createReferralLink(any(UserDto.class), any(ReferralLinkCreateRequest.class)))
            .thenReturn(responseDto);

        // When & Then
        mockMvc.perform(post("/api/referral_link")
                .content(objectMapper.writeValueAsString(createRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(print())
            .andExpect(jsonPath("$.status").value(ResponseStatusCode.SUCCESS.getCode()))
            .andExpect(jsonPath("$.data.link").value("https://short.link/abc123"))
            .andExpect(jsonPath("$.data.target").value("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&openinapp=true&autoTriggerApp=true&fastrender=true&backstack=true"))
            .andExpect(jsonPath("$.data.pageUrl").value("https://www.hktvmall.com/s/H000001"))
            .andExpect(jsonPath("$.data.storeCode").value("H000001"))
            .andExpect(jsonPath("$.data.createdTime").exists());
    }

    @Test
    void getReferralLinkPage_ShouldReturnBothLinkAndTargetForAllItems() throws Exception {
        // Given
        ReferralLinkPageResponse item1 = ReferralLinkPageResponse.builder()
            .link("https://short.link/item1")
            .target("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&utm_source=google&utm_medium=cpc")
            .pageUrl("https://www.hktvmall.com/s/H000001")
            .storeCode("H000001")
            .createdTime(System.currentTimeMillis())
            .build();

        ReferralLinkPageResponse item2 = ReferralLinkPageResponse.builder()
            .link("https://short.link/item2")
            .target("https://www.hktvmall.com/s/H000002?utm_campaign=attr_H000002&utm_source=facebook&utm_medium=social")
            .pageUrl("https://www.hktvmall.com/s/H000002")
            .storeCode("H000002")
            .createdTime(System.currentTimeMillis())
            .build();

        PageResult<ReferralLinkPageResponse> pageResult = PageResult.<ReferralLinkPageResponse>builder()
            .pageNumber(1)
            .pageSize(10)
            .totalItems(2)
            .totalPages(1)
            .list(List.of(item1, item2))
            .build();

        ResponseDto<PageResult<ReferralLinkPageResponse>> responseDto = ResponseDto.success(pageResult);
        when(referralLinkUseCase.getReferralLinkPage(any(UserDto.class), eq(10), eq(1), any(Set.class)))
            .thenReturn(responseDto);

        // When & Then
        mockMvc.perform(get("/api/referral_link")
                .param("pageSize", "10")
                .param("pageNumber", "1")
                .param("storefrontStoreCodes", "H000001,H000002")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(print())
            .andExpect(jsonPath("$.status").value(ResponseStatusCode.SUCCESS.getCode()))
            .andExpect(jsonPath("$.data.totalItems").value(2))
            .andExpect(jsonPath("$.data.list").isArray())
            .andExpect(jsonPath("$.data.list[0].link").value("https://short.link/item1"))
            .andExpect(jsonPath("$.data.list[0].target").value("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&utm_source=google&utm_medium=cpc"))
            .andExpect(jsonPath("$.data.list[0].storeCode").value("H000001"))
            .andExpect(jsonPath("$.data.list[1].link").value("https://short.link/item2"))
            .andExpect(jsonPath("$.data.list[1].target").value("https://www.hktvmall.com/s/H000002?utm_campaign=attr_H000002&utm_source=facebook&utm_medium=social"))
            .andExpect(jsonPath("$.data.list[1].storeCode").value("H000002"));
    }

    @Test
    void createReferralLink_WithComplexUTMParameters_ShouldReturnFullTargetURL() throws Exception {
        // Given
        createRequest.setSources(List.of("google", "facebook"));
        createRequest.setMediums(List.of("cpc", "social"));
        createRequest.setCampaignDescription("summer_sale");
        createRequest.setCampaignDate("20240701");

        ReferralLinkPageResponse complexResponse = ReferralLinkPageResponse.builder()
            .link("https://short.link/complex123")
            .target("https://www.hktvmall.com/s/H000001?utm_source=google+facebook&utm_medium=cpc+social&utm_campaign=attr_H000001_summer_sale_20240701&openinapp=true&autoTriggerApp=true&fastrender=true&backstack=true")
            .pageUrl("https://www.hktvmall.com/s/H000001")
            .storeCode("H000001")
            .campaignDescription("summer_sale")
            .campaignDate("20240701")
            .createdTime(System.currentTimeMillis())
            .build();

        ResponseDto<ReferralLinkPageResponse> responseDto = ResponseDto.success(complexResponse);
        when(referralLinkUseCase.createReferralLink(any(UserDto.class), any(ReferralLinkCreateRequest.class)))
            .thenReturn(responseDto);

        // When & Then
        mockMvc.perform(post("/api/referral_link")
                .content(objectMapper.writeValueAsString(createRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(print())
            .andExpect(jsonPath("$.status").value(ResponseStatusCode.SUCCESS.getCode()))
            .andExpect(jsonPath("$.data.link").value("https://short.link/complex123"))
            .andExpect(jsonPath("$.data.target").value("https://www.hktvmall.com/s/H000001?utm_source=google+facebook&utm_medium=cpc+social&utm_campaign=attr_H000001_summer_sale_20240701&openinapp=true&autoTriggerApp=true&fastrender=true&backstack=true"))
            .andExpect(jsonPath("$.data.campaignDescription").value("summer_sale"))
            .andExpect(jsonPath("$.data.campaignDate").value("20240701"));
    }

    @Test
    void createReferralLink_WhenTargetIsNull_ShouldHandleGracefully() throws Exception {
        // Given
        ReferralLinkPageResponse responseWithNullTarget = ReferralLinkPageResponse.builder()
            .link("https://short.link/abc123")
            .target(null)
            .pageUrl("https://www.hktvmall.com/s/H000001")
            .storeCode("H000001")
            .createdTime(System.currentTimeMillis())
            .build();

        ResponseDto<ReferralLinkPageResponse> responseDto = ResponseDto.success(responseWithNullTarget);
        when(referralLinkUseCase.createReferralLink(any(UserDto.class), any(ReferralLinkCreateRequest.class)))
            .thenReturn(responseDto);

        // When & Then
        mockMvc.perform(post("/api/referral_link")
                .content(objectMapper.writeValueAsString(createRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(print())
            .andExpect(jsonPath("$.status").value(ResponseStatusCode.SUCCESS.getCode()))
            .andExpect(jsonPath("$.data.link").value("https://short.link/abc123"))
            .andExpect(jsonPath("$.data.target").isEmpty())
            .andExpect(jsonPath("$.data.storeCode").value("H000001"));
    }
}
